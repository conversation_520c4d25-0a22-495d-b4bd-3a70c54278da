import { useState, useCallback, useMemo } from 'react';
import { updateTeam } from '@/services/teamsService';
import { toast } from '@/toast/toast';
import { type Player } from '@/types/player';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';

interface UseTeamCaptainProps {
  teamId: string;
  players: Player[];
  currentCaptainId?: string;
  onCaptainUpdate: (captainId: string | null) => void;
}

interface UseTeamCaptainReturn {
  showCaptainModal: boolean;
  isUpdating: boolean;
  playerOptions: Option[];
  openCaptainModal: () => void;
  closeCaptainModal: () => void;
  handleCaptainSelect: (selected: Option | Option[] | null) => Promise<void>;
}

export function useTeamCaptain({
  teamId,
  players,
  currentCaptainId,
  onCaptainUpdate,
}: UseTeamCaptainProps): UseTeamCaptainReturn {
  const [showCaptainModal, setShowCaptainModal] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Convert players to options for AsyncSelectWithSearch
  const playerOptions = useMemo(() => {
    return players.map((player) => ({
      label: player.name,
      value: player.id,
      player,
    }));
  }, [players]);

  const openCaptainModal = useCallback(() => {
    setShowCaptainModal(true);
  }, []);

  const closeCaptainModal = useCallback(() => {
    setShowCaptainModal(false);
  }, []);

  const handleCaptainSelect = useCallback(
    async (selected: Option | Option[] | null) => {
      if (!selected || Array.isArray(selected)) {
        return;
      }

      setIsUpdating(true);
      try {
        const { success, error } = await updateTeam({
          teamId,
          team: { captain_id: selected.value },
        });

        if (!success) {
          toast.error(error || 'Failed to set team captain');
          return;
        }

        // Update local state
        onCaptainUpdate(selected.value);
        toast.success(`${selected.label} is now the team captain`);
        closeCaptainModal();
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        setIsUpdating(false);
      }
    },
    [teamId, onCaptainUpdate, closeCaptainModal]
  );

  return {
    showCaptainModal,
    isUpdating,
    playerOptions,
    openCaptainModal,
    closeCaptainModal,
    handleCaptainSelect,
  };
}
