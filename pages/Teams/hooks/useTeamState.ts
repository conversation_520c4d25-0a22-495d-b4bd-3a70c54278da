import { useCallback } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { type Team } from '@/types/teams';
import { type Player } from '@/types/player';
import {
  teamDataState,
  teamPlayersState,
  teamWithCaptainSelector,
  updateTeamData,
  updateTeamPlayers,
} from '@/atoms/teamState';

interface UseTeamStateProps {
  teamId: string;
  initialTeam?: Team;
}

interface UseTeamStateReturn {
  team: Team | null;
  setTeam: (team: Team) => void;
  updateTeam: (updates: Partial<Team>) => void;
  setPlayers: (players: Player[]) => void;
  updateCaptain: (captainId: string | null, captainName?: string) => void;
}

export function useTeamState({
  teamId,
  initialTeam,
}: UseTeamStateProps): UseTeamStateReturn {
  const [teamData, setTeamData] = useRecoilState(teamDataState);
  const [playersData, setPlayersData] = useRecoilState(teamPlayersState);
  const getTeamWithCaptain = useRecoilValue(teamWithCaptainSelector);

  // Get team with captain information
  const team = getTeamWithCaptain(teamId);

  // Initialize team data if not present
  const setTeam = useCallback(
    (teamToSet: Team) => {
      setTeamData(updateTeamData(teamId, teamToSet));
    },
    [teamId, setTeamData]
  );

  // Update team data
  const updateTeam = useCallback(
    (updates: Partial<Team>) => {
      setTeamData(updateTeamData(teamId, updates));
    },
    [teamId, setTeamData]
  );

  // Set players for the team
  const setPlayers = useCallback(
    (players: Player[]) => {
      setPlayersData(updateTeamPlayers(teamId, players));
    },
    [teamId, setPlayersData]
  );

  // Update captain with both ID and name
  const updateCaptain = useCallback(
    (captainId: string | null, captainName?: string) => {
      const updates: Partial<Team> = {
        captain_id: captainId,
      };
      
      // If captain name is provided, use it; otherwise it will be computed from players
      if (captainName) {
        updates.captain_name = captainName;
      }
      
      setTeamData(updateTeamData(teamId, updates));
    },
    [teamId, setTeamData]
  );

  // Initialize team data if provided and not already set
  if (initialTeam && !teamData[teamId]) {
    setTeam(initialTeam);
  }

  return {
    team: team || teamData[teamId] || null,
    setTeam,
    updateTeam,
    setPlayers,
    updateCaptain,
  };
}
