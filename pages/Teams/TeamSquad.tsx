import React from 'react';
import { View, ScrollView } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import LogoImage from '@/components/k-components/LogoImage';
import NoDataFound from '@/components/k-components/NoDataFound';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { Spinner } from '@/components/ui/spinner';
import { type Team } from '@/types/teams';
import { type Player } from '@/types/player';
import { useSquadManagement } from './hooks/useSquadManagement';
import { Button, ButtonText } from '@/components/ui/button';
import { router } from 'expo-router';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { PlusIcon } from 'lucide-react-native';
import { PlayerListCard } from '../Players/components/PlayerListCard';

interface TeamSquadProps {
  team: Team;
  tournamentId: string;
}

const TeamSquad: React.FC<TeamSquadProps> = ({ team, tournamentId }) => {
  const { players, loading, playerOperations, loadPlayers } =
    useSquadManagement({
      teamId: team.id,
      tournamentId,
    });

  const handleEditSqaud = (mode: 'create' | 'edit') => {
    router.push({
      pathname: '/screens/teams/[team-id]/squad',
      params: {
        'team-id': team.id,
        'tournament-id': tournamentId,
        mode: mode,
      },
    });
  };

  if (loading) {
    return <FullscreenLoader />;
  }
  return (
    <View className="flex-1 px-4 py-4">
      {players.length === 0 ? (
        <NoDataFound
          title="No Players"
          subtitle="This team currently has no players."
          action={
            <CTAButton
              title="Add Players"
              lefticon={PlusIcon}
              onPress={() => handleEditSqaud('create')}
            />
          }
        />
      ) : (
        <VStack className="space-y-3">
          <HStack className="items-center justify-between px-3">
            <Text className="text-xl font-urbanistBold text-typography-900 mb-2 mt-2">
              Players ({players.length})
            </Text>
            <View className="flex-row gap-3 items-center">
              <Button variant="link" onPress={() => {}}>
                <ButtonText className="font-urbanistBold text-sm text-primary-0">
                  Set Captian
                </ButtonText>
              </Button>
              <Button
                size="xs"
                variant="outline"
                onPress={() => handleEditSqaud('edit')}
              >
                <ButtonText className="font-urbanistBold text-sm">
                  Edit Squad
                </ButtonText>
              </Button>
            </View>
          </HStack>

          {players.map((player: Player) => (
            <PlayerListCard key={player.id} player={player} />
          ))}
        </VStack>
      )}
    </View>
  );
};

export default TeamSquad;
