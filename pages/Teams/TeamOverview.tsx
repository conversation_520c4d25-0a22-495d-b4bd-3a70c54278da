import React, { memo } from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { type Team } from '@/types/teams';
import LogoImage from '@/components/k-components/LogoImage';
import ActionRequired from '@/components/k-components/ActionRequired';
import Jersey from '@/components/k-components/Jersey';
import SocialLinksSection from '@/components/k-components/SocialLinksSection';
import { useTeamSocialLinks } from './hooks/useTeamSocialLinks';
import { every, isEmpty } from 'lodash';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import { getInitialTeamInfoFormData } from '@/config/teamInfoFormConfig';
import { TeamMotto } from './components/TeamMotto';
import ListInfoCard from '@/components/util-components/ListInfoCard';
import { MailIcon, PhoneIcon, UserIcon } from 'lucide-react-native';

interface TeamOverviewProps {
  team: Team;
}

const TeamHeader = memo(({ team }: { team: Team }) => {
  const { socialLinks, handleSocialLinkSave } = useTeamSocialLinks(team.id);

  return (
    <View className="flex flex-col">
      <View className="flex flex-row items-center justify-between pt-8 px-3 w-full">
        <View className="flex flex-row items-center gap-4 flex-1 min-w-0">
          <LogoImage
            logoUrl={team.logo_url}
            fallbackText={team.name}
            width={90}
            height={90}
            borderRadius={60}
            fallBacktextClassName="text-3xl font-urbanistBold"
          />
          <View className="flex flex-col justify-start flex-1 pr-2 min-w-0">
            <Text
              className="text-typography-700 font-urbanistBold text-2xl"
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              {team?.name?.trim()}
            </Text>
            <Text className="text-md font-urbanistSemiBold text-typography-600 tracking-widest">
              {team?.short_name?.trim()}
            </Text>
          </View>
        </View>
        {team?.jersey_color && (
          <Jersey
            color={team?.jersey_color}
            name={team.short_name}
            number="1"
            width={70}
            height={70}
          />
        )}
      </View>
      <View className="w-full flex-row items-center gap-1 px-4">
        <View className="flex-1 h-px bg-gray-300" />
        {!isEmpty(socialLinks) ? (
          <SocialLinksSection
            socialLinks={socialLinks}
            onSave={handleSocialLinkSave}
          />
        ) : (
          <View style={{ height: 84, width: '100%' }} />
        )}
        <View className="flex-1 h-px bg-gray-300" />
      </View>
    </View>
  );
});

const TeamOverview: React.FC<TeamOverviewProps> = ({ team }) => {
  const router = useRouter();
  const teamInfo = getInitialTeamInfoFormData(team);
  const teamInfoMissing = every(teamInfo, isEmpty);

  const handleSetTeamInfo = () => {
    router.push({
      pathname: SCREENS.TEAM_INFO,
      params: {
        'team-id': team.id,
      },
    });
  };

  const infoItems = [
    {
      icon: UserIcon,
      value: !isEmpty(team?.captain_name)
        ? `Captian: ${team?.captain_name}`
        : '',
    },
    {
      icon: UserIcon,
      value: !isEmpty(team?.owner_name)
        ? `Team Owner: ${team?.owner_name}`
        : '',
    },
    {
      icon: UserIcon,
      value: !isEmpty(team?.coach_name)
        ? `Team Coach: ${team?.coach_name}`
        : '',
    },
  ];

  const contactItems = [
    {
      icon: PhoneIcon,
      value: team?.team_phone || 'N/A',
      copyable: true,
    },
    {
      icon: MailIcon,
      value: team?.team_email || 'N/A',
      copyable: true,
    },
  ];

  return (
    <View className="flex-1 ">
      <TeamHeader team={team} />
      <View className="h-full px-4">
        <TeamMotto motto={team?.tagline} />
        <ActionRequired
          actions={[
            {
              id: 'set-team-info',
              label: 'Set Team Info',
              description:
                'Add important details like team owner, coach name, and contact details to finalize team setup.',
              condition: teamInfoMissing,
              onPress: handleSetTeamInfo,
            },
          ]}
        />
        <View className="pt-6">
          <ListInfoCard title="Team Info" items={infoItems} />
        </View>
        <View className="pt-6">
          <ListInfoCard title="Team Contact" items={contactItems} />
        </View>
      </View>
    </View>
  );
};

export default TeamOverview;
