import React from 'react';
import { View } from 'react-native';
import TournamentCardInfo from './components/TournamentCardInfo';
import TournamentRulesInfo from './components/TournamentRulesInfo';
import dayjs from 'dayjs';
import { type Tournament } from './types';
import TournamentStatusBadge from './components/TournamentStatusBadge';
import ActionRequired from '@/components/k-components/ActionRequired';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import { sportRulesConfig } from '@/config/sportRulesConfig';
import { ScreenMode } from '@/app/screens/tournament/[tournament-id]/rules';

interface Props {
  tournament: Tournament;
}

const TournamentOverview = ({ tournament }: Props) => {
  const router = useRouter();

  const tournamentRulesExist =
    !!sportRulesConfig[tournament.sport_type as keyof typeof sportRulesConfig];
  const tournamentRulesMissing =
    Object.keys(tournament?.tournament_rules || {}).length === 0;
  console.log('>>>>>', tournament?.tournament_rules);

  return (
    <View className="p-5">
      <TournamentStatusBadge
        start_date={tournament.start_date}
        end_date={tournament.end_date}
      />

      <TournamentCardInfo tournament={tournament} />
      <TournamentRulesInfo tournament={tournament} />
      <ActionRequired
        actions={[
          {
            id: 'set-rules',
            label: 'Set Tournament Rules',
            description:
              'Define rules such as team size, gender category, and format to complete tournament setup.',
            condition: tournamentRulesExist && tournamentRulesMissing,
            onPress: () =>
              router.push({
                pathname: SCREENS.TOURNAMENT_RULES,
                params: {
                  'tournament-id': tournament.id,
                  sport_type: tournament.sport_type,
                  mode: ScreenMode.CREATE,
                },
              }),
          },
        ]}
      />
    </View>
  );
};

export default TournamentOverview;
