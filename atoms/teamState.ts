import { atom, selector } from 'recoil';
import { type Team } from '@/types/teams';
import { type Player } from '@/types/player';

export const teamDataState = atom<{
  [teamId: string]: Team;
}>({
  key: 'teamDataState',
  default: {},
});

export const teamPlayersState = atom<{
  [teamId: string]: Player[];
}>({
  key: 'teamPlayersState',
  default: {},
});

export const teamWithCaptainSelector = selector({
  key: 'teamWithCaptainSelector',
  get: ({ get }) => {
    return (teamId: string): Team | null => {
      const teams = get(teamDataState);
      const playersMap = get(teamPlayersState);

      const team = teams[teamId];
      const players = playersMap[teamId] || [];

      if (!team) return null;

      // Find captain player and add captain_name to team
      const captain = players.find((player) => player.id === team.captain_id);

      return {
        ...team,
        captain_name: captain?.name || undefined,
      };
    };
  },
});

// Helper function to update team data
export const updateTeamData = (teamId: string, teamData: Partial<Team>) => {
  return (currentState: { [teamId: string]: Team }) => ({
    ...currentState,
    [teamId]: {
      ...currentState[teamId],
      ...teamData,
    },
  });
};

export const updateTeamPlayers = (teamId: string, players: Player[]) => {
  return (currentState: { [teamId: string]: Player[] }) => ({
    ...currentState,
    [teamId]: players,
  });
};
