import { SPORTS } from '@/config/sportsConfig';
import { getSportLabel } from '@/utils/sports-utils';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';

export const tournamentFormSchema = [
  {
    key: 'tournamentDuration',
    title: 'Tournament Dates',
    description: 'When will the tournament take place?',
    type: 'date-range',
    required: true,
    validate: (value: { startDate?: string; endDate?: string }) => {
      return Boolean(value?.startDate && value?.endDate);
    },
  },
  {
    key: 'name',
    title: 'Tournament Name',
    description: 'Give your tournament a unique and catchy name.',
    proTip: 'Short names are more memorable!',
    type: 'text',
    validate: (value: string) => value && value.trim().length > 0,
    required: true,
  },
  {
    key: 'type',
    title: 'Sport Type',
    description: 'Select the sport for which you want to host the tournament.',
    proTip: 'Choose the correct sport to match your tournament format.',
    type: 'select',
    options: [
      {
        value: SPORTS.FIELD_HOCKEY,
        displayName: getSportLabel(SPORTS.FIELD_HOCKEY),
        iconComponent: MaterialIcons,
        iconName: 'sports-hockey',
        iconColor: 'black',
      },
      {
        value: SPORTS.PICKLEBALL,
        displayName: getSportLabel(SPORTS.PICKLEBALL),
        iconComponent: MaterialIcons,
        iconName: 'sports-tennis',
        iconColor: 'black',
      },
    ],
    validate: (value: string) => value && value !== '',
    required: true,
  },
  {
    key: 'venue',
    title: 'Tournament Venue',
    description: 'Tell us the exact venue where the tournament will be held.',
    proTip: 'Example: "Kanteerava Stadium" or "Green Park Arena".',
    type: 'text',
    validate: (value: string) => value && value.trim().length > 0,
    required: true,
  },
  {
    key: 'location',
    title: 'Location',
    description:
      'Select the city, state or region where the tournament will be held.',
    proTip: 'Select a state or area for better discovery.',
    type: 'location',
    required: true,
  },
  {
    key: 'logo',
    title: 'Upload Logo',
    description: 'Add your tournament logo to make it look professional.',
    proTip: 'Center your icon — edge-to-edge designs may get cropped.',
    type: 'file-upload',
  },
];
