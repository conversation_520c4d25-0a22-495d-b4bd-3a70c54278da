import { supabase } from '@/lib/supabase';
import { TeamFormData, TeamSocialLinks, TeamInfoData } from '@/types/teams';

export const checkIfTeamNameExists = async (
  tournamentId: string,
  name: string,
  excludeTeamId?: string
) => {
  let query = supabase
    .from('teams')
    .select('id')
    .eq('tournament_id', tournamentId)
    .ilike('name', name);

  if (excludeTeamId) {
    query = query.neq('id', excludeTeamId);
  }
  const { data, error } = await query;

  if (error) {
    return false;
  }

  return data.length > 0;
};

export const checkIfShortNameExists = async (
  tournamentId: string,
  shortName: string,
  excludeTeamId?: string
) => {
  let query = supabase
    .from('teams')
    .select('id')
    .eq('tournament_id', tournamentId)
    .ilike('short_name', shortName);

  // Exclude current team when editing
  if (excludeTeamId) {
    query = query.neq('id', excludeTeamId);
  }

  const { data, error } = await query;

  if (error) {
    return false;
  }

  return data.length > 0;
};

export const createTeam = async ({
  tournamentId,
  team,
}: {
  tournamentId: string;
  team: TeamFormData;
}) => {
  const { data: createdTeam, error } = await supabase
    .from('teams')
    .insert([
      {
        tournament_id: tournamentId,
        ...team,
      },
    ])
    .select()
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, team: createdTeam };
};

export const fetchTeamById = async (teamId: string) => {
  const { data: team, error } = await supabase
    .from('teams')
    .select('*')
    .eq('id', teamId)
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, team };
};

export const fetchTeams = async ({
  tournamentId,
  search = '',
  page = 1,
  limit = 10,
}: {
  tournamentId: string;
  search?: string;
  page?: number;
  limit?: number;
}) => {
  const offset = (page - 1) * limit;

  let baseQuery = supabase
    .from('teams')
    .select('*', { count: 'exact' })
    .eq('tournament_id', tournamentId);

  if (search) {
    baseQuery = baseQuery.ilike('name', `%${search}%`);
  }

  const { data, count, error } = await baseQuery
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    return { teams: [], count: 0, error: error.message };
  }

  return {
    teams: data,
    count: count || 0,
    error: null,
  };
};

export const updateTeam = async ({
  teamId,
  team,
}: {
  teamId: string;
  team: TeamFormData | TeamSocialLinks | TeamInfoData;
}) => {
  const { data: updatedTeam, error } = await supabase
    .from('teams')
    .update(team)
    .eq('id', teamId)
    .select()
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, team: updatedTeam };
};

export const deleteTeamById = async (teamId: string) => {
  const { error } = await supabase.from('teams').delete().eq('id', teamId);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
};

export const fetchTeamSocialLinksById = async (teamId: string) => {
  const { data, error } = await supabase
    .from('teams')
    .select('website, instagram_url, facebook_url')
    .eq('id', teamId)
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, socialLinks: data };
};
