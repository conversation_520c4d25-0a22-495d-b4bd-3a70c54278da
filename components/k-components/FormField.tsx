import React, { useState, useCallback, useMemo, useRef } from 'react';
import {
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
} from '@/components/ui/form-control';
import { Input, InputField } from '@/components/ui/input';
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectItem,
  SelectIcon,
} from '@/components/ui/select';
import {
  Radio,
  RadioGroup,
  RadioIndicator,
  RadioLabel,
  RadioIcon,
} from '@/components/ui/radio';
import {
  AlertCircleIcon,
  ChevronDownIcon,
  CircleIcon,
} from '@/components/ui/icon';
import { Spinner } from '@/components/ui/spinner';
import { Text } from '@/components/ui/text';
import { View } from '@/components/ui/view';
import FormFieldLabel from './FormFieldLabel';
import { VStack } from '../ui/vstack';
import UploadImageField from '../field-components/UploadImageField';
import ColorPicker from '../field-components/ColorPicker';

export enum FieldType {
  TEXT = 'text',
  NUMBER = 'number',
  SELECT = 'select',
  RADIO = 'radio',
  UPLOAD_IMAGE = 'upload_image',
  COLOR_PICKER = 'color_picker',
}

export interface Option {
  label: string;
  value: string;
}

export interface FieldConfig {
  key: string;
  label: string;
  type: FieldType | string;
  required?: boolean;
  info?: string;
  default?: any;
  options?: Option[];
  validators?: ((value: any) => string | null | Promise<string | null>)[];
  bucketName?: string; // For upload image fields
  folderPath?: string; // For upload image fields
  inputProps?: React.ComponentProps<typeof InputField>; // For input field props
}

interface FieldProps {
  type: FieldType;
  keyName: string;
  field: FieldConfig;
  value: any;
  error?: string;
  disabled?: boolean;
  onChange: (key: string, value: any) => void;
  inputprops?: React.ComponentProps<typeof InputField>;
}

const FormField: React.FC<FieldProps> = React.memo(
  ({
    type,
    keyName,
    field,
    value,
    error,
    onChange,
    disabled = false,
    inputprops,
  }) => {
    const [internalError, setInternalError] = useState<string | null>(null);
    const [isValidating, setIsValidating] = useState<boolean>(false);
    const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const validateField = useCallback(
      async (val: any) => {
        if (!val || field.validators?.length === 0) {
          setInternalError(null);
          setIsValidating(false);
          return;
        }

        // Clear any existing timeout
        if (validationTimeoutRef.current) {
          clearTimeout(validationTimeoutRef.current);
        }

        setIsValidating(true);
        setInternalError(null);

        try {
          for (const validate of field.validators ?? []) {
            const result = validate(val);

            // Handle both sync and async validators
            const err = result instanceof Promise ? await result : result;

            if (err) {
              setInternalError(err);
              setIsValidating(false);
              return;
            }
          }

          setInternalError(null);
        } catch (error) {
          setInternalError('Validation error occurred');
        } finally {
          setIsValidating(false);
        }
      },
      [field.validators]
    );

    const handleChange = useCallback(
      (val: string) => {
        onChange(keyName, val);

        // Debounce validation for async validators
        if (validationTimeoutRef.current) {
          clearTimeout(validationTimeoutRef.current);
        }

        validationTimeoutRef.current = setTimeout(() => {
          validateField(val);
        }, 200);
      },
      [keyName, onChange, validateField]
    );

    // Cleanup timeout on unmount
    React.useEffect(() => {
      return () => {
        if (validationTimeoutRef.current) {
          clearTimeout(validationTimeoutRef.current);
        }
      };
    }, []);

    const selectedOption = useMemo(
      () => field.options?.find((opt) => opt.value === value),
      [field.options, value]
    );

    const inputProps = {
      value: value ?? '',
      onBlur: () => {
        // Clear debounce timeout and validate immediately on blur
        if (validationTimeoutRef.current) {
          clearTimeout(validationTimeoutRef.current);
        }
        validateField(value);
      },
      className: 'font-urbanistMedium text-base',
      onChangeText: handleChange,
      ...field.inputProps, // Props from field configuration
      ...inputprops, // Props passed directly to component (override field props)
    };

    const renderInput = () => {
      switch (type) {
        case FieldType.TEXT:
          return (
            <Input size="lg" isDisabled={disabled}>
              <InputField {...inputProps} />
            </Input>
          );

        case FieldType.NUMBER:
          return (
            <Input size="lg" isDisabled={disabled}>
              <InputField
                {...inputProps}
                inputMode="numeric"
                keyboardType="phone-pad"
                value={String(value ?? '')}
                onChangeText={(val) => {
                  const cleaned = val.replace(/[^0-9]/g, '');
                  handleChange(cleaned);
                }}
              />
            </Input>
          );

        case FieldType.SELECT:
          if (!Array.isArray(field.options)) {
            console.warn(
              `Field "${field.key}" is missing valid options array.`
            );
            return null;
          }

          return (
            <Select
              isDisabled={disabled}
              selectedValue={value}
              onValueChange={handleChange}
              className="font-urbanistMedium"
            >
              <SelectTrigger
                size="lg"
                className="flex flex-row items-center justify-between"
              >
                <SelectInput
                  placeholder="Select"
                  value={selectedOption?.label || 'Select'}
                  className="font-urbanistMedium text-base self-center"
                />
                <SelectIcon className="mr-3" as={ChevronDownIcon} />
              </SelectTrigger>
              <SelectPortal>
                <SelectBackdrop />
                <SelectContent className="font-urbanistMedium">
                  {field.options.map((option) => (
                    <SelectItem
                      key={option.value}
                      label={option.label}
                      value={option.value}
                      className="rounded-full my-1"
                      textStyle={{
                        className: `
                          ${
                            option.value === value
                              ? 'text-primary-0 font-urbanistBold'
                              : ''
                          }
                          font-urbanistMedium`,
                      }}
                    />
                  ))}
                </SelectContent>
              </SelectPortal>
            </Select>
          );

        case FieldType.RADIO:
          if (!Array.isArray(field.options)) return null;

          return (
            <RadioGroup
              isDisabled={disabled}
              value={value}
              onChange={handleChange}
            >
              <VStack space="sm" className="mt-2">
                {field.options.map((option) => (
                  <Radio key={option.value} value={option.value} size="md">
                    <RadioIndicator>
                      <RadioIcon as={CircleIcon} />
                    </RadioIndicator>
                    <RadioLabel className="text-base font-urbanistMedium">
                      {option.label}
                    </RadioLabel>
                  </Radio>
                ))}
              </VStack>
            </RadioGroup>
          );

        case FieldType.UPLOAD_IMAGE:
          return (
            <UploadImageField
              value={value || ''}
              onChange={(url) => handleChange(url)}
              bucketName={field.bucketName || 'default-bucket'}
              folderPath={field.folderPath}
            />
          );

        case FieldType.COLOR_PICKER:
          return (
            <ColorPicker
              value={value || ''}
              onChange={(color) => handleChange(color)}
              label={field.label}
            />
          );

        default:
          return null;
      }
    };

    return (
      <FormControl
        isRequired={field.required}
        isInvalid={!!(error || internalError)}
        className="my-2"
      >
        <FormFieldLabel label={field.label} info={field.info} />
        {renderInput()}
        {isValidating && (
          <View className="flex-row items-center mt-1">
            <Spinner color="#1DB960" size={13} className="mr-1" />
            <Text className="text-sm text-typography-500 font-urbanistMedium tracking-wider">
              Checking...
            </Text>
          </View>
        )}
        {!!(error || internalError) && !isValidating && (
          <FormControlError>
            <FormControlErrorIcon size="xs" as={AlertCircleIcon} />
            <FormControlErrorText className="font-urbanist text-sm">
              {error || internalError}
            </FormControlErrorText>
          </FormControlError>
        )}
      </FormControl>
    );
  }
);

export default FormField;
