import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import React, { useCallback, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { NavLayout } from '@/components/NavLayout';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import FormField, { FieldType } from '@/components/k-components/FormField';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import {
  Accordion,
  AccordionItem,
  AccordionHeader,
  AccordionTrigger,
  AccordionIcon,
  AccordionContent,
} from '@/components/ui/accordion';
import { PlusIcon, MinusIcon } from 'lucide-react-native';
import { fetchTeamById, updateTeam } from '@/services/teamsService';
import { validateEmail, validatePhone } from '@/utils';
import { toast } from '@/toast/toast';
import {
  teamInfoFormFields,
  teamInfoSectionFields,
  teamContactSectionFields,
  type TeamInfoFormData,
  getInitialTeamInfoFormData,
} from '@/config/teamInfoFormConfig';

export default function TeamInfoScreen() {
  const router = useRouter();
  const { 'team-id': teamId } = useLocalSearchParams();
  const insets = useSafeAreaInsets();
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<TeamInfoFormData>(
    getInitialTeamInfoFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  const loadTeamData = useCallback(() => {
    async function fetchData() {
      if (!teamId) return;

      setLoading(true);
      const { success, team, error } = await fetchTeamById(teamId as string);

      if (!success) {
        toast.error(error || 'Failed to load team data');
        router.back();
        return;
      }

      setFormData(getInitialTeamInfoFormData(team));
      setLoading(false);
    }

    fetchData();
  }, [teamId, router]);

  useFocusEffect(loadTeamData);

  const handleFieldChange = (key: string, value: any) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
    if (errors[key]) {
      setErrors((prev) => ({ ...prev, [key]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (formData.team_email) {
      const emailError = validateEmail(formData.team_email);
      if (emailError) {
        newErrors.team_email = emailError;
      }
    }

    if (formData.team_phone) {
      const phoneError = validatePhone(formData.team_phone);
      if (phoneError) {
        newErrors.team_phone = phoneError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    const { success, error } = await updateTeam({
      teamId: teamId as string,
      team: formData,
    });

    setIsSubmitting(false);

    if (!success) {
      toast.error(error || 'Failed to update team info. Please try again.');
      return;
    }

    toast.success('Team info updated successfully');
    router.back();
  };

  const handleCancel = () => {
    router.back();
  };

  const renderFormField = (field: any) => (
    <FormField
      key={field.key}
      type={field.type as FieldType}
      keyName={field.key}
      field={field}
      value={formData[field.key as keyof TeamInfoFormData]}
      error={errors[field.key]}
      onChange={handleFieldChange}
    />
  );

  if (loading) {
    return <FullscreenLoader />;
  }

  return (
    <NavLayout title="Team Info" noScroll={true}>
      <View className="flex-1">
        <ScrollView
          className="flex-1 px-4"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 120 }}
        >
          <VStack space="lg" className="py-6">
            {/* Team Tagline */}
            {teamInfoFormFields.map(renderFormField)}

            {/* Team Info Section in Accordion */}
            <Accordion
              type="multiple"
              defaultValue={['team-info']}
              className="border-none !p-0 !m-0 rounded-md overflow-hidden shadow-none"
            >
              <AccordionItem value="team-info">
                <AccordionHeader>
                  <AccordionTrigger className="px-0">
                    {({ isExpanded }) => (
                      <>
                        <Text className="font-urbanistBold text-typography-800">
                          Team Info
                        </Text>
                        <AccordionIcon as={isExpanded ? MinusIcon : PlusIcon} />
                      </>
                    )}
                  </AccordionTrigger>
                </AccordionHeader>
                <AccordionContent className="pt-2 px-0">
                  <VStack space="md">
                    {teamInfoSectionFields.map(renderFormField)}
                  </VStack>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            {/* Team Contact Section in Accordion */}
            <Accordion
              type="multiple"
              defaultValue={['team-contact']}
              className="border-none !p-0 !m-0 rounded-md overflow-hidden shadow-none"
            >
              <AccordionItem value="team-contact">
                <AccordionHeader>
                  <AccordionTrigger className="px-0">
                    {({ isExpanded }) => (
                      <>
                        <Text className="font-urbanistBold text-typography-800">
                          Team Contact
                        </Text>
                        <AccordionIcon as={isExpanded ? MinusIcon : PlusIcon} />
                      </>
                    )}
                  </AccordionTrigger>
                </AccordionHeader>
                <AccordionContent className="pt-2 px-0">
                  <VStack space="md">
                    {teamContactSectionFields.map(renderFormField)}
                  </VStack>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </VStack>
        </ScrollView>

        {/* Sticky Bottom Buttons */}
        <View
          className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2"
          style={{ paddingBottom: Math.max(insets.bottom, 16) }}
        >
          <VStack space="sm">
            <Text
              className="text-center text-typography-600 font-urbanistMedium pb-1"
              onPress={handleCancel}
            >
              Cancel
            </Text>
            <CTAButton
              title="Save"
              onPress={handleSave}
              loading={isSubmitting}
            />
          </VStack>
        </View>
      </View>
    </NavLayout>
  );
}
